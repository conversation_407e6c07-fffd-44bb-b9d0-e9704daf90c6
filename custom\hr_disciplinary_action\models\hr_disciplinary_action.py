from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, date
from dateutil.relativedelta import relativedelta

class HrDisciplinaryAction(models.Model):
    _name = 'hr.disciplinary.action'
    _description = 'Disciplinary Action'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'creation_date desc'
    
    name = fields.Char(string='Reference', readonly=True, copy=False, default='New')
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True, tracking=True)
    type_id = fields.Many2one('hr.disciplinary.type', string='Type', required=True, tracking=True)
    action = fields.Selection([
        ('sp1', 'SP I'),
        ('sp2', 'SP II'),
        ('sp3', 'SP III')
    ], string='Action', required=True, tracking=True)
    creation_date = fields.Date(string='Creation Date', default=fields.Date.today, readonly=True)
    effective_date = fields.Date(string='Effective Date', tracking=True,
                                help='Date when the disciplinary action becomes effective (after approval)')
    validity_period = fields.Integer(string='Validity Period', default=2, 
                                    help='Validity period in months', tracking=True)
    validity_end_date = fields.Date(string='Valid Until', compute='_compute_validity_end_date', store=True)
    additional_salary_deduction = fields.Boolean(string='Additional Salary Deduction', tracking=True)
    salary_deduction_percentage = fields.Float(string='Salary Deduction Percentage', tracking=True)
    salary_deduction_validity = fields.Integer(string='Salary Deduction Validity', default=2,
                                            help='Number of months the salary deduction is valid', tracking=True)
    remaining_validity = fields.Integer(string='Remaining Validity', compute='_compute_remaining_validity', store=True,
                                      help='Remaining months of salary deduction validity')
    note = fields.Text(string='Note')
    submitter_id = fields.Many2one('res.users', string='Submitter', default=lambda self: self.env.user,
                                 readonly=True)
    approver_id = fields.Many2one('res.users', string='Approver', readonly=True, tracking=True)
    attachment_ids = fields.Many2many('ir.attachment', string='Attachments')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('to_approve', 'To Approve'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancel', 'Cancelled')
    ], string='Status', default='draft', tracking=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    current_user_is_approver = fields.Boolean(
        string='Current User Is Approver',
        compute='_compute_current_user_is_approver',
    )
    
    @api.depends_context('uid')
    def _compute_current_user_is_approver(self):
        for record in self:
            # Check if user is an administrator (has HR Manager rights)
            is_admin = self.env.user.has_group('hr.group_hr_manager')
            
            # Check if user is an SP approver
            current_employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
            is_sp_approver = current_employee and current_employee.is_sp_approver or False
            
            # User can approve if they're either an admin or an SP approver
            record.current_user_is_approver = is_admin or is_sp_approver
    
    @api.model
    def create(self, vals):
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('hr.disciplinary.action') or 'New'
        return super(HrDisciplinaryAction, self).create(vals)
    
    @api.depends('effective_date', 'validity_period')
    def _compute_validity_end_date(self):
        for record in self:
            if record.effective_date and record.validity_period:
                record.validity_end_date = fields.Date.from_string(record.effective_date) + relativedelta(months=record.validity_period)
            else:
                record.validity_end_date = False
    
    @api.depends('effective_date', 'salary_deduction_validity')
    def _compute_remaining_validity(self):
        today = fields.Date.from_string(fields.Date.today())
        for record in self:
            if record.effective_date and record.salary_deduction_validity and record.state == 'approved':
                end_date = fields.Date.from_string(record.effective_date) + relativedelta(months=record.salary_deduction_validity)
                if end_date > today:
                    # Calculate remaining months
                    remaining_months = (end_date.year - today.year) * 12 + (end_date.month - today.month)
                    record.remaining_validity = max(0, remaining_months)
                else:
                    record.remaining_validity = 0
            else:
                record.remaining_validity = 0
    
    def action_submit(self):
        self.write({'state': 'to_approve'})
        return True
    
    def action_approve(self):
        # Check if user is an administrator (has HR Manager rights)
        is_admin = self.env.user.has_group('hr.group_hr_manager')
        
        # Check if user is an SP approver
        current_employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        is_sp_approver = current_employee and current_employee.is_sp_approver or False
        
        if not (is_admin or is_sp_approver):
            raise ValidationError(_("You don't have permission to approve Disciplinary Actions. Only administrators or employees marked as 'SP Approver' can approve."))
        
        self.write({
            'state': 'approved',
            'effective_date': fields.Date.today(),
            'approver_id': self.env.user.id
        })
        return True
    
    def action_reject(self):
        # Check if user is an administrator (has HR Manager rights)
        is_admin = self.env.user.has_group('hr.group_hr_manager')
        
        # Check if user is an SP approver
        current_employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        is_sp_approver = current_employee and current_employee.is_sp_approver or False
        
        if not (is_admin or is_sp_approver):
            raise ValidationError(_("You don't have permission to reject Disciplinary Actions. Only administrators or employees marked as 'SP Approver' can reject."))
        
        self.write({'state': 'rejected'})
        return True
    
    def action_reset_to_draft(self):
        self.write({'state': 'draft'})
        return True
    
    def action_cancel(self):
        self.write({'state': 'cancel'})
        return True
    
    @api.constrains('salary_deduction_percentage')
    def _check_salary_deduction_percentage(self):
        for record in self:
            if record.additional_salary_deduction and record.salary_deduction_percentage <= 0:
                raise ValidationError(_("Salary deduction percentage must be greater than 0."))
            if record.salary_deduction_percentage > 100:
                raise ValidationError(_("Salary deduction percentage cannot exceed 100%."))
    
    @api.onchange('additional_salary_deduction')
    def _onchange_additional_salary_deduction(self):
        if not self.additional_salary_deduction:
            self.salary_deduction_percentage = 0.0
            self.salary_deduction_validity = 0
