<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Disciplinary Action Tree View -->
    <record id="view_hr_disciplinary_action_tree" model="ir.ui.view">
        <field name="name">hr.disciplinary.action.tree</field>
        <field name="model">hr.disciplinary.action</field>
        <field name="arch" type="xml">
            <tree string="Disciplinary Actions" decoration-info="state == 'draft'" decoration-warning="state == 'to_approve'" decoration-success="state == 'approved'" decoration-danger="state == 'rejected'">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="type_id"/>
                <field name="action"/>
                <field name="creation_date"/>
                <field name="effective_date"/>
                <field name="validity_period"/>
                <field name="additional_salary_deduction"/>
                <field name="salary_deduction_percentage" attrs="{'invisible': [('additional_salary_deduction', '=', False)]}"/>
                <field name="remaining_validity" attrs="{'invisible': [('additional_salary_deduction', '=', False)]}"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Disciplinary Action Form View -->
    <record id="view_hr_disciplinary_action_form" model="ir.ui.view">
        <field name="name">hr.disciplinary.action.form</field>
        <field name="model">hr.disciplinary.action</field>
        <field name="arch" type="xml">
            <form string="Disciplinary Action">
                <header>
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                    <button name="action_approve" string="Approve" type="object" class="oe_highlight" 
                            attrs="{'invisible': ['|', ('state', '!=', 'to_approve'), ('current_user_is_approver', '=', False)]}"/>
                    <button name="action_reject" string="Reject" type="object" class="btn-danger" 
                            attrs="{'invisible': ['|', ('state', '!=', 'to_approve'), ('current_user_is_approver', '=', False)]}"/>
                    <button name="action_reset_to_draft" string="Reset to Draft" type="object" states="rejected"/>
                    <button name="action_cancel" string="Cancel" type="object" class="btn-secondary" states="draft,to_approve,approved,rejected"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,to_approve,approved,rejected,cancel"/>
                    <field name="current_user_is_approver" invisible="1"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="employee_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="type_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="action" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="creation_date" readonly="1"/>
                            <field name="effective_date"/>
                        </group>
                        <group>
                            <field name="validity_period" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="validity_end_date" readonly="1"/>
                            <field name="additional_salary_deduction" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="salary_deduction_percentage" attrs="{'invisible': [('additional_salary_deduction', '=', False)], 'readonly': [('state', '!=', 'draft')], 'required': [('additional_salary_deduction', '=', True)]}"/>
                            <field name="salary_deduction_validity" attrs="{'invisible': [('additional_salary_deduction', '=', False)], 'readonly': [('state', '!=', 'draft')], 'required': [('additional_salary_deduction', '=', True)]}"/>
                            <field name="remaining_validity" attrs="{'invisible': [('additional_salary_deduction', '=', False)]}" readonly="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="note" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                    </group>
                    <group>
                        <group>
                            <field name="submitter_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="approver_id" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Attachments">
                            <field name="attachment_ids" widget="many2many_binary" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Disciplinary Action Search View -->
    <record id="view_hr_disciplinary_action_search" model="ir.ui.view">
        <field name="name">hr.disciplinary.action.search</field>
        <field name="model">hr.disciplinary.action</field>
        <field name="arch" type="xml">
            <search string="Search Disciplinary Actions">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="type_id"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="To Approve" name="to_approve" domain="[('state', '=', 'to_approve')]"/>
                <filter string="Approved" name="approved" domain="[('state', '=', 'approved')]"/>
                <filter string="Rejected" name="rejected" domain="[('state', '=', 'rejected')]"/>
                <separator/>
                <filter string="With Salary Deduction" name="with_deduction" domain="[('additional_salary_deduction', '=', True)]"/>
                <filter string="Active Deductions" name="active_deductions" domain="[('additional_salary_deduction', '=', True), ('remaining_validity', '>', 0), ('state', '=', 'approved')]"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Type" name="type" context="{'group_by': 'type_id'}"/>
                    <filter string="Action" name="action" context="{'group_by': 'action'}"/>
                    <filter string="Status" name="status" context="{'group_by': 'state'}"/>
                    <filter string="Creation Date" name="creation_date" context="{'group_by': 'creation_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Disciplinary Action Action -->
    <record id="action_hr_disciplinary_action" model="ir.actions.act_window">
        <field name="name">Disciplinary Actions</field>
        <field name="res_model">hr.disciplinary.action</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_hr_disciplinary_action_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new disciplinary action
            </p>
            <p>
                Create disciplinary actions for employees and track their validity.
            </p>
        </field>
    </record>
</odoo>
